/*Font - Metrophobic*/
@import url('https://fonts.googleapis.com/css2?family=Metrophobic&display=swap');

/* Font - Lato */
@import url('https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap');

/* Font - League Spartan */
@import url('https://fonts.googleapis.com/css2?family=League+Spartan:wght@100..900&display=swap');

/* Font - Montserrat */
@import url('https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap');


@tailwind base;
@tailwind components;
@tailwind utilities;


* {
  user-select: none;
}
input, textarea {
  user-select: text;
}


@keyframes strokeMove {
    0% {
      stroke-dashoffset: 312; /* Matches the circumference */
    }
    100% {
      stroke-dashoffset: 0; /* Stroke moves inward */
    }
  }
  
  .animate-stroke circle {
    animation: strokeMove 2s linear infinite;
  }

/* Hero.css */
.loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 8em;
  height: 8em;
}

.loader::before,
.loader::after {
  position: absolute;
  content: "";
  height: 9em;
  width: 9em;
  border: 0.4em solid #FF7731; /* Initial border width */
  border-radius: 50%;
  background-color: rgba(255, 119, 49, 0.2); /* Light fill color */
  opacity: 0.5; /* Initial opacity */
  transform: scale(0.4); /* Initial scale */
  transition: all 0.5s ease; /* Smooth transition for hover and reset */
}

.loader::after {
  opacity: 0.5;
  animation-delay: 1s;
}

/* Start animation on hover */
.loader:hover::before,
.loader:hover::after {
  border: 0.2em solid #FF7731; /* Border width at 50% */
  transform: scale(0.7); /* Scale at 50% */
  opacity: 0.5; /* Opacity at 50% */
  transition: all 0.5s ease; /* Smooth transition for hover */
}

/* Smoothly reset to initial state when hover is removed */
.loader::before,
.loader::after {
  transition: all 0.5s ease; /* Smooth transition for reset */
}

/* Hero Button */

.hero-loader {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 10em;
  height: 10em;
}

.hero-loader::before,
.hero-loader::after {
  position: absolute;
  content: "";
  height: 10em;
  width: 10em;
  border: 0.4em solid #fe99ac; /* Initial border width */
  border-radius: 50%;
  background-color: #f7acba41; /* Light fill color */
  opacity: 0.5; /* Initial opacity */
  transform: scale(0.4); /* Initial scale */
  transition: all 0.5s ease; /* Smooth transition for hover and reset */
}

.hero-loader::after {
  opacity: 0.5;
  animation-delay: 1s;
}

/* Start animation on hover */
.hero-loader:hover::before,
.hero-loader:hover::after {
  border: 0.2em solid #fe99ac; /* Border width at 50% */
  transform: scale(0.7); /* Scale at 50% */
  opacity: 0.5; /* Opacity at 50% */
  transition: all 0.5s ease; /* Smooth transition for hover */
}

/* Smoothly reset to initial state when hover is removed */
.hero-loader::before,
.hero-loader::after {
  transition: all 0.5s ease; /* Smooth transition for reset */
}


/* Scrollbar */

::-webkit-scrollbar {
  display: none; /* Chrome, Safari, Opera */
}

/* Links */
.link {
  color: #333237;
  
  text-decoration: none;
  position: relative;
  display: inline-block;
}

.link::before,
.link::after {
  content: "";
  position: absolute;
  background-color: #FF7731;
  width: 0%;
  height: 2px;
  bottom: 0;
  transition: width 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.link::before {
  left: 0;
}

.link::after {
  right: 0;
}

.link:hover::before,
.link:hover::after {
  width: 100%;
}


/* autofill color */

input:-webkit-autofill,
input:-webkit-autofill:hover,
input:-webkit-autofill:focus,
input:-webkit-autofill:active {
  background-color: transparent !important;
  color: #263238 !important;
  -webkit-box-shadow: 0 0 0px 1000px #F9F8F2 inset !important;
}

.hero-section{
  position: relative;
  overflow: hidden;
}

.vertical-bike {
  position: absolute;
  width: 150px;
  height: auto;
  opacity: 0;
  transition: opacity 0.5s ease-in-out, transform 0.5s ease-in-out;
}


/* .vertical-bike {
  display: inline-block; 
  animation: moveHorizontal 20s linear infinite;
}

@keyframes moveHorizontal {
  0% {
    transform: translateX(-100%); 
  }
  100% {
    transform: translateX(100vw); 
  }
} */

.walking .left-leg {
  animation: leftLeg 0.4s infinite alternate ease-in-out;
}
.walking .right-leg {
  animation: rightLeg 0.4s infinite alternate ease-in-out;
}

@keyframes leftLeg {
  0% { transform: rotate(-5deg); transform-origin: top; }
  50% { transform: rotate(5deg); transform-origin: top; }
  100% { transform: rotate(-5deg); transform-origin: top; }
}

@keyframes rightLeg {
  0% { transform: rotate(5deg); transform-origin: top; }
  50% { transform: rotate(-5deg); transform-origin: top; }
  100% { transform: rotate(5deg); transform-origin: top; }
}

/* Hide scrollbar for cards section */
.scrollbar-hide {
  -ms-overflow-style: none;  /* Internet Explorer 10+ */
  scrollbar-width: none;  /* Firefox */
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;  /* Safari and Chrome */
}


