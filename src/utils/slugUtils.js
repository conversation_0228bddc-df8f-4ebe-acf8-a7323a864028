// Utility function to convert blog title to URL-friendly slug
export const createSlug = (title) => {
  if (!title) return '';
  
  return title
    .toLowerCase()
    .trim()
    .replace(/[^\w\s-]/g, '') // Remove special characters except spaces and hyphens
    .replace(/\s+/g, '-') // Replace spaces with hyphens
    .replace(/-+/g, '-') // Replace multiple hyphens with single hyphen
    .replace(/^-+|-+$/g, ''); // Remove leading/trailing hyphens
};

// Utility function to find blog by slug
export const findBlogBySlug = (blogs, slug) => {
  return blogs.find(blog => createSlug(blog.title) === slug);
};

// Utility function to find blog by ID (fallback)
export const findBlogById = (blogs, id) => {
  return blogs.find(blog => blog.id === parseInt(id));
}; 