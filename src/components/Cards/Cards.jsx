import React, { useRef, useState, useEffect } from "react";
import arrowDown from "/assets/Arrow_up.svg";

const ExcellenceCards = () => {
  const scrollContainerRef = useRef(null);
  const [canScrollLeft, setCanScrollLeft] = useState(false);
  const [canScrollRight, setCanScrollRight] = useState(true);

  // Check scroll position to show/hide buttons
  const checkScrollPosition = () => {
    if (scrollContainerRef.current) {
      const { scrollLeft, scrollWidth, clientWidth } =
        scrollContainerRef.current;
      setCanScrollLeft(scrollLeft > 0);
      setCanScrollRight(scrollLeft < scrollWidth - clientWidth - 1);
    }
  };

  // Scroll functions
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -320, // Scroll by approximately one card width
        behavior: "smooth",
      });
    }
  };

  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 320, // Scroll by approximately one card width
        behavior: "smooth",
      });
    }
  };

  // Check scroll position on mount and scroll
  useEffect(() => {
    const container = scrollContainerRef.current;
    if (container) {
      checkScrollPosition();
      container.addEventListener("scroll", checkScrollPosition);
      return () => container.removeEventListener("scroll", checkScrollPosition);
    }
  }, []);

  // Check scroll position on window resize
  useEffect(() => {
    const handleResize = () => {
      setTimeout(checkScrollPosition, 100);
    };
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);
  const cards = [
    {
      title: "Speed & Security",
      backgroundColor: "bg-[rgba(0,255,229,0.1)]",
      hoverBgColor: "bg-[rgba(0,255,229,0.5)]",
      borderColor: "border-[rgba(0,255,229,0.8)]",
      number: "04",
      circleColors: ["bg-[rgba(0,255,229,0.25)]", "bg-[rgba(0,255,229,0.5)]"],
    },
    {
      title: "Environment",
      backgroundColor: "bg-orange-50",
      hoverBgColor: "bg-orange-100/50",
      borderColor: "border-orange-200",
      number: "03",
      circleColors: ["bg-orange-100/50", "bg-orange-200/50"],
    },
    {
      title: "Technology",
      backgroundColor: "bg-purple-50",
      hoverBgColor: "bg-purple-100/50",
      borderColor: "border-purple-200",
      number: "02",
      circleColors: ["bg-purple-100/50", "bg-purple-200/50"],
    },
    {
      title: "Innovation",
      backgroundColor: "bg-blue-50",
      hoverBgColor: "bg-blue-100/50",
      borderColor: "border-blue-200",
      number: "01",
      circleColors: ["bg-blue-100/50", "bg-blue-200/50"],
    },
  ];

  // Arrow SVG as inline component to ensure proper display
  const ArrowIcon = () => (
    <svg
      width="24"
      height="24"
      viewBox="0 0 24 24"
      fill="none"
      className="w-full h-full"
    >
      <path
        d="M7 17L17 7M17 7H7M17 7V17"
        stroke="currentColor"
        strokeWidth="2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );

  return (
    <div className="w-full max-w-[95%] mt-10 mx-auto p-4 sm:p-6 md:p-8 lg:p-16 xl:p-20 flex flex-col gap-6 md:gap-8 lg:gap-12 bg-gray-50 rounded-2xl md:rounded-3xl">
      {/* Title Section */}
      <div className="flex flex-col justify-center items-center text-center">
        <h2 className="text-orange-500 text-2xl sm:text-3xl md:text-4xl font-bold">
          Years of
        </h2>
        <h1 className="text-slate-900 text-3xl sm:text-4xl md:text-5xl font-bold mt-1 sm:mt-2">
          Excellence
        </h1>
      </div>

      {/* Cards Section with Navigation Buttons */}
      <div className="w-full relative">
        {/* Left Navigation Button */}
        <button
          onClick={scrollLeft}
          className={`absolute left-2 top-1/2 -translate-y-1/2 z-20 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-300 hover:bg-orange-50 hover:border-orange-300 ${
            canScrollLeft
              ? "opacity-100 cursor-pointer"
              : "opacity-50 cursor-not-allowed"
          }`}
          disabled={!canScrollLeft}
        >
          <svg
            className="w-5 h-5 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M15 19l-7-7 7-7"
            />
          </svg>
        </button>

        {/* Right Navigation Button */}
        <button
          onClick={scrollRight}
          className={`absolute right-2 top-1/2 -translate-y-1/2 z-20 w-10 h-10 rounded-full bg-white shadow-lg border border-gray-200 flex items-center justify-center transition-all duration-300 hover:bg-orange-50 hover:border-orange-300 ${
            canScrollRight
              ? "opacity-100 cursor-pointer"
              : "opacity-50 cursor-not-allowed"
          }`}
          disabled={!canScrollRight}
        >
          <svg
            className="w-5 h-5 text-gray-600"
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth={2}
              d="M9 5l7 7-7 7"
            />
          </svg>
        </button>

        {/* Horizontal scrolling container - no scrollbar */}
        <div
          ref={scrollContainerRef}
          className="overflow-x-auto scrollbar-hide snap-x snap-mandatory md:snap-none"
          style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
        >
          <div className="flex gap-1 md:gap-8 py-8 min-w-max px-6 md:px-12">
            {cards.map((card, index) => (
              <div
                key={index}
                className="relative flex-shrink-0 snap-start md:snap-align-none"
              >
                {/* Divider Lines - only show on desktop between cards */}
                {index < cards.length - 1 && (
                  <div className="hidden md:block absolute -right-4 top-0 h-full z-10">
                    <div className="h-full w-px bg-gradient-to-b from-transparent via-gray-300 to-transparent"></div>
                  </div>
                )}

                {/* Card */}
                <div
                  className={`group relative overflow-hidden cursor-pointer w-[calc(100vw-4rem)] md:w-80 lg:w-96 h-48 sm:h-56 md:h-64 rounded-2xl md:rounded-3xl ${card.backgroundColor} border ${card.borderColor} transition-all duration-500 ease-out md:hover:scale-105 hover:shadow-lg`}
                >
                  {/* Expanding Circle Background */}
                  <div className="absolute right-0 bottom-0 w-32 sm:w-40 md:w-48 h-32 sm:h-40 md:h-48">
                    <div
                      className={`absolute right-0 bottom-0 w-32 sm:w-40 md:w-48 h-32 sm:h-40 md:h-48 rounded-full ${card.circleColors[0]} transition-all duration-500 ease-out transform scale-100 group-hover:scale-150 translate-x-1/4 translate-y-1/4`}
                    ></div>
                    <div
                      className={`absolute right-0 bottom-0 w-24 sm:w-32 md:w-36 h-24 sm:h-32 md:h-36 rounded-full ${card.circleColors[1]} transition-all duration-500 ease-out transform scale-100 group-hover:scale-150 translate-x-1/4 translate-y-1/4`}
                    ></div>
                  </div>

                  {/* Large Background Number */}
                  <div className="absolute top-4 sm:top-6 md:top-8 left-4 sm:left-6 md:left-8">
                    <span className="text-4xl sm:text-5xl md:text-6xl leading-none font-bold opacity-50 text-black z-[999]">
                      {card.number}
                    </span>
                  </div>

                  {/* Content Container */}
                  <div className="absolute bottom-4 sm:bottom-6 md:bottom-8 left-4 sm:left-6 md:left-8 right-4 sm:right-6 md:right-8 z-10">
                    <div className="flex justify-between items-end">
                      <h3 className="text-lg sm:text-xl md:text-2xl font-semibold text-gray-800 flex-1 mr-4">
                        {card.title}
                      </h3>

                      {/* Arrow Icon Container - Fixed positioning and sizing */}
                      <div className="flex-shrink-0 w-6 h-6 sm:w-7 sm:h-7 md:w-8 md:h-8 text-gray-600 transition-all duration-300 group-hover:rotate-45 group-hover:text-gray-800">
                        <img 
                        src={arrowDown}
                        alt="Arrow Down"
                        />
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default ExcellenceCards;
