import React, { useState, useEffect } from "react";
import { useMute } from "../Work/MuteContext"; // Import context

import image_01 from "/assets/successImg01.svg";
import successBg_01 from "/assets/successBg_image_01.svg";
import image_02 from "/assets/successImg_02.svg";
import image_03 from "/assets/successImg_03.svg";
import card_01 from "/assets/successCard_01.svg";
import card_02 from "/assets/successCard_02.svg";
import button from "/assets/successBtn.svg";
import button_Arrow from "/assets/Btn_Arrow.svg";
import employee_01 from "/assets/employee_01.jpeg";
import employee_02 from "/assets/employee_02.jpeg";
import employee_03 from "/assets/employee_03.jpeg";
import employee_04 from "/assets/employee_04.jpeg";
import employee_05 from "/assets/employee_05.jpeg";
import employee_06 from "/assets/employee_06.jpeg";
import employee_07 from "/assets/employee_07.jpeg";
import employee_08 from "/assets/employee_08.jpeg";
import employee_09 from "/assets/employee_09.jpeg";

function Success() {
  const { setIsMuted } = useMute(); // Access the context
  useEffect(() => {
    // When the Success section becomes visible, mute the video in Work section
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsMuted(true); // Set the mute status to true when Success section is visible
        }
      },
      { threshold: 0.5 }
    );

    const successSection = document.querySelector("#success-section"); // The Success section
    if (successSection) {
      observer.observe(successSection);
    }

    return () => {
      if (successSection) {
        observer.unobserve(successSection);
      }
    };
  }, [setIsMuted]);

  const images = [
    employee_01,
    employee_02,
    employee_03,
    employee_04,
    employee_05,
    employee_06,
    employee_07,
    employee_08,
    employee_09,
  ];

  const [currentIndex, setCurrentIndex] = useState(1); // Start at first real image
  const [isTransitioning, setIsTransitioning] = useState(false);
  const totalImages = images.length;

  // Create array with cloned images at start and end
  const clonedImages = [images[totalImages - 1], ...images, images[0]];

  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prev) => prev + 1);
    }, 2000);

    return () => clearInterval(interval);
  }, []);

  useEffect(() => {
    if (currentIndex === totalImages + 1) {
      // When we reach the last clone, wait for transition to complete then jump to first real image
      setTimeout(() => {
        setIsTransitioning(true);
        setCurrentIndex(1);
        setTimeout(() => {
          setIsTransitioning(false);
        }, 50);
      }, 1000);
    } else if (currentIndex === 0) {
      // When we reach the first clone, wait for transition to complete then jump to last real image
      setTimeout(() => {
        setIsTransitioning(true);
        setCurrentIndex(totalImages);
        setTimeout(() => {
          setIsTransitioning(false);
        }, 50);
      }, 1000);
    }
  }, [currentIndex, totalImages]);

  return (
    <div id="success-section" className="overflow-hidden">
      {/* Top Text */}
      <p className="font-lato text-[#0e202b3d] opacity-10 font-extrabold xl:text-[196px] lg:text-[150px] md:text-[100px] text-[70px]">
        Years of
      </p>
      <div className="flex flex-col md:flex-row justify-center items-center lg:-mt-16 md:-mt-5">
        {/* Left Div */}
        <div className="w-[90%] sm:w-[70%] md:w-[40%] h-[342px] md:h-[600px] mx-auto md:mx-5">
          {/* Image - 01 */}
          <div className="relative">
            <img
              src={successBg_01}
              alt="Image-01"
              className="hidden md:block"
            />
            {/* Photo Frame Container - Image_02 */}
            <div className="">
              <div className="absolute top-[15%] xl:-left-[10%] xl:w-96 lg:w-80 lg:left-[8%] md:w-60 md:left-[10%] md:top-[30%] lg:top-[15%] mx-auto w-72 left-0 right-0">
                {/* Frame Border */}
                <div className="relative bg-[#FF7731] p-3 shadow-lg rounded-xl overflow-hidden">
                  {/* Inner Frame Border */}
                  <div className="relative w-full rounded-xl overflow-hidden">
                    {/* Carousel */}
                    <div
                      className="flex"
                      style={{
                        transform: `translateX(-${currentIndex * 100}%)`,
                        transition: isTransitioning
                          ? "none"
                          : "transform 1000ms ease-in-out",
                      }}
                    >
                      {clonedImages.map((image, index) => (
                        <div
                          key={index}
                          className="flex-shrink-0 w-full aspect-square"
                        >
                          <img
                            src={image}
                            alt={`Employee ${index}`}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            </div>
            {/* Image - 03 */}
            <img
              src={image_03}
              alt="Image-03"
              className="absolute xl:top-[80%] xl:left-[50%] xl:w-56 lg:w-40 lg:top-[63%] lg:left-[55%] md:w-28 md:top-[80%] md:left-[65%] mx-auto w-36 right-0 -top-2 rounded-xl"
            />
          </div>
        </div>
        {/* Right Div */}
        <div className="flex flex-col justify-center items-center md:flex-none md:justify-start md:items-start w-[90%] sm:w-[80%] md:w-[60%] h-auto md:h-[600px] px-[20px]">
          <p className="font-lato font-bold xl:text-[50.25px] xl:leading-[100px] lg:text-[40px] lg:leading-[70px] md:text-[30px] md:leading-[50px] text-[24px] text-center md:text-left leading-[40px]">
            Are you ready to grow then align paths with{" "}
            <span className="text-[#FF7731]">Flexion</span>
          </p>
          <p className="font-metrophobic text-[#333237] font-normal xl:text-[24px] xl:leading-[56px] lg:text-[20px] lg:leading-[40px] md:text-[16px] md:leading-[30px] sm:text-[14px] sm:leading-[24px] mt-1 text-center md:text-left">
            We are the experts for your exponential growth. Our solutions lead
            to excellent customer experience.
          </p>
          {/* Cards Section */}
          <div className="mt-10 flex flex-row  gap-6 justify-start">
            {/* Card 1 */}
            <div
              className="border-l-4 border-[#FF7731] p-2 rounded-lg shadow-2xl 
  xl:w-72 lg:w-64 md:w-50 sm:w-[48%] flex items-center gap-4 
  md:hover:scale-105 transition-transform duration-300 will-change-transform transform-gpu"
            >
              <div className="lg:p-4 p-1 rounded-full">
                <img src={card_01} alt="Card 01" className="w-12 sm:w-16" />
              </div>

              <div className="flex flex-col justify-center items-center text-center transform transition-transform duration-300">
                <p className="font-lato font-bold lg:text-lg md:text-sm text-sm">
                  IT Consultant
                </p>
                <p className="font-lato text-[#6A6E7C] text-xs text-[10px]">
                  Experience Team members
                </p>
              </div>
            </div>

            {/* Card 2 */}
            <div className="border-l-4 border-[#FF7731] p-2 rounded-lg shadow-2xl xl:w-72 lg:w-64 md:w-50 sm:w-[48%] flex items-center gap-4 md:hover:scale-105 transition duration-300 will-change-transform transform-gpu">
              <div className="lg:p-4 p-1 rounded-full">
                <img src={card_02} alt="Card 02" className="w-12 sm:w-16" />
              </div>
              <div className="flex flex-col justify-center items-center text-center">
                <p className="font-lato font-bold lg:text-lg md:text-sm text-sm">
                  IT Specialist
                </p>
                <p className="font-lato text-[#6A6E7C] text-xs text-[10px]">
                  Experience Team members
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Button */}
      {/* <div className="flex justify-end place-items-start xl:-mt-32 lg:-mt-48 md:-mt-[270px] mt-6 z-[40] mr-10 cursor-pointer">
        <button className="flex justify-center items-center gap-2 relative z-[40]">
          <p className="loader w-[250px] md:w-[300px] xl:w-[400px] h-[50px] sm:h-[60px] md:h-[80px] -mr-[85px]  md:-mr-[177px] lg:-mr-[178px] xl:-mr-[228px]" />
          <p className="font-metrophobic font-normal text-xs  md:text-base lg:text-lg text-[#343434]">
            Read more Success
          </p>
          <img
            src={button_Arrow}
            alt="Arrow"
            className="w-3 h-3 sm:w-4 sm:h-4 md:w-6 md:h-6 mt-1"
          />
        </button>
      </div> */}
      {/* Bottom Text */}
      <div className="flex justify-end items-end lg:-mt-40 md:-mt-72 sm:-mt-8">
        <p className="font-eSpartan text-[#0e202b3d] opacity-10 font-extrabold xl:text-[196px] lg:text-[150px] md:text-[100px] text-[70px] lg:z-[30]">
          Excellence
        </p>
      </div>
    </div>
  );
}

export default Success;
