import React, { createContext, useState, useContext } from "react";

// Create a context for mute state
const MuteContext = createContext();

// Create a provider component
export function MuteProvider({ children }) {
  const [isMuted, setIsMuted] = useState(true); // Default mute state
  console.log("Muted",isMuted);
  

  const toggleMute = () => {
    setIsMuted((prevMute) => !prevMute);
  };

  return (
    <MuteContext.Provider value={{ isMuted, setIsMuted, toggleMute }}>
      {children}
    </MuteContext.Provider>
  );
}

// Custom hook to use mute context
export function useMute() {
  return useContext(MuteContext);
}
