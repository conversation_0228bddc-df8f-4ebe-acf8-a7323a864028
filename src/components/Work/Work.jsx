import React, { useRef, useEffect, useState } from "react";
// import videoBg from "";
import { FontAwesomeIcon } from "@fortawesome/react-fontawesome";
import { faVolumeXmark, faVolumeHigh } from "@fortawesome/free-solid-svg-icons";
import { useMute } from "./MuteContext";

function Work() {
  const videoRef = useRef(null);
  const { isMuted, toggleMute } = useMute(); // Use context for mute state
  const [isLoading, setIsLoading] = useState(true); // Track video loading state

  const handleMute = () => {
    toggleMute(); // Toggle mute/unmute
  };

  useEffect(() => {
    if (videoRef.current) {
      videoRef.current.muted = isMuted;
    }
  }, [isMuted]);

  useEffect(() => {
    const observer = new IntersectionObserver(
      ([entry]) => {
        if (videoRef.current) {
          if (entry.isIntersecting) {
            videoRef.current.play();
          } else {
            videoRef.current.pause();
          }
        }
      },
      { threshold: 0.1 }
    );

    if (videoRef.current) {
      observer.observe(videoRef.current);
    }

    return () => {
      if (videoRef.current) {
        observer.unobserve(videoRef.current);
      }
    };
  }, []);

  return (
    <div id="work-story" className="overflow-hidden h-screen relative">
      {/* Background Video */}
      <video
        ref={videoRef}
        loop  
        muted={isMuted}
        preload="none"
        playsInline
        className="absolute top-0 left-0 w-full h-full object-cover inset-0"
        onLoadedData={() => setIsLoading(false)}
      >
        <source
          src="https://ffmpeg.funkybugsinfo.co.in/stream"
          type="video/mp4"
        />
        Your browser does not support the video tag.
      </video>

      {/* Loading Placeholder */}
      {isLoading && (
        <div className="absolute top-0 left-0 w-full h-full bg-black flex items-center justify-center">
          <p className="text-white text-lg">Loading video...</p>
        </div>
      )}

      {/* Overlay */}
      <div className="absolute top-0 left-0 w-full h-full bg-black bg-opacity-75"></div>

      {/* Text Content */}
      <div className="relative flex flex-col w-full h-screen justify-start items-center lg:items-start mx-auto lg:mx-0 lg:pl-20">
        <p className="font-lato text-[#FF7731] font-normal text-4xl sm:text-5xl md:text-6xl mt-14">
          Solutions core
        </p>
        <p className="font-lato text-[#FFFFFF] font-bold text-5xl sm:text-6xl md:text-7xl text-center mt-12">
          Our Work Story
        </p>
      </div>

      {/* Bottom Text Centered */}
      <div className="absolute bottom-0 md:left-1/2 md:transform md:-translate-x-1/2 mb-8 px-2">
        <p className="font-metrophobic font-normal text-base sm:text-lg md:text-xl text-[#FFFFFF] text-center">
          We are mastered in providing the market-leading solutions, Here's the
          story of our tech experts.
        </p>
      </div>

      {/* Mute/Unmute Button */}
      <button
        onClick={handleMute}
        className="absolute top-5 right-5 bg-opacity-50 text-white p-2 rounded-full flex items-center justify-center w-10 h-10"
      >
        {isMuted ? (
          <FontAwesomeIcon icon={faVolumeXmark} />
        ) : (
          <FontAwesomeIcon icon={faVolumeHigh} />
        )}
      </button>
    </div>
  );
}

export default Work;
