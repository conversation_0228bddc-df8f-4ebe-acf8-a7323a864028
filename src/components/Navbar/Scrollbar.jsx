import { useEffect, useRef, useState } from "react";

const Scrollbar = ({ children }) => {
  const scrollContainerRef = useRef(null);
  const scrollThumbRef = useRef(null);
  const [isScrolling, setIsScrolling] = useState(false);
  const [isDragging, setIsDragging] = useState(false);
  const [startY, setStartY] = useState(0);
  const [startScrollTop, setStartScrollTop] = useState(0);

  useEffect(() => {
    const scrollContainer = scrollContainerRef.current;
    const scrollThumb = scrollThumbRef.current;

    if (!scrollContainer || !scrollThumb) return;

    const updateScrollbar = () => {
      const containerHeight = scrollContainer.clientHeight;
      const contentHeight = scrollContainer.scrollHeight;
      const scrollRatio = containerHeight / contentHeight;
      const thumbHeight = Math.max(containerHeight * scrollRatio, 30);

      scrollThumb.style.height = `${thumbHeight}px`;
      scrollThumb.style.top = `${
        (scrollContainer.scrollTop / contentHeight) * containerHeight
      }px`;
    };

    const showScrollbar = () => {
      setIsScrolling(true);
      clearTimeout(window.scrollTimer);
      window.scrollTimer = setTimeout(() => setIsScrolling(false), 1000);
    };

    const handleScroll = () => {
      updateScrollbar();
      showScrollbar();
    };

    scrollContainer.addEventListener("scroll", handleScroll);
    updateScrollbar();

    return () => scrollContainer.removeEventListener("scroll", handleScroll);
  }, []);

  const handleMouseDown = (e) => {
    setIsDragging(true);
    setStartY(e.clientY);
    setStartScrollTop(scrollContainerRef.current.scrollTop);
    document.body.style.userSelect = "none";
  };

  const handleMouseMove = (e) => {
    if (!isDragging) return;
    const deltaY = e.clientY - startY;
    const contentHeight =
      scrollContainerRef.current.scrollHeight -
      scrollContainerRef.current.clientHeight;
    scrollContainerRef.current.scrollTop =
      startScrollTop +
      (deltaY / scrollContainerRef.current.clientHeight) * contentHeight;
  };

  const handleMouseUp = () => {
    setIsDragging(false);
    document.body.style.userSelect = "";
  };

  useEffect(() => {
    document.addEventListener("mousemove", handleMouseMove);
    document.addEventListener("mouseup", handleMouseUp);
    return () => {
      document.removeEventListener("mousemove", handleMouseMove);
      document.removeEventListener("mouseup", handleMouseUp);
    };
  }, [isDragging]);

  return (
    <div className="relative h-screen w-full overflow-hidden">
      {/* Scrollable Content */}
      <div
        ref={scrollContainerRef}
        className="h-full overflow-y-scroll scroll-smooth"
        style={{ scrollBehavior: "smooth" }}
      >
        {children}
      </div>

      {/* Custom Scrollbar (Hidden on Mobile) */}
      <div className="absolute top-0 right-0 w-[12px] h-full pointer-events-none z-[999] hidden md:block">
        <div
          ref={scrollThumbRef}
          className={`w-full bg-[#FF7731] rounded-lg transition-opacity ${
            isScrolling ? "opacity-100" : "opacity-0"
          } ${isDragging ? "opacity-100" : ""}`}
          style={{ position: "absolute", right: 0, cursor: "pointer" }}
          onMouseDown={handleMouseDown}
        ></div>
      </div>
    </div>
  );
};

export default Scrollbar;
