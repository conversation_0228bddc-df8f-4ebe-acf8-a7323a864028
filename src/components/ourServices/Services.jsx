import React, { useState, useEffect } from "react";
import { ChevronDown, ChevronUp } from "lucide-react";
import { motion, AnimatePresence } from "framer-motion";
import mascot from "/assets/mascot.png";

const Services = () => {
  const [selectedService, setSelectedService] = useState("Database");
  const [isMobile, setIsMobile] = useState(false);
  const [expandedAccordion, setExpandedAccordion] = useState(null);

  useEffect(() => {
    const checkIfMobile = () => {
      setIsMobile(window.innerWidth < 1024);
    };
    checkIfMobile();
    window.addEventListener("resize", checkIfMobile);
    return () => window.removeEventListener("resize", checkIfMobile);
  }, []);

  const services = [
    {
      title: "Database",
      icon: "/assets/database.svg",
      bgColor: "bg-[#428BC11A]",
      borderColor: "border-[#7E9EA9]",
      heading: "From Skyline to the Shoreline, \nWe are here",
      description:
        "We provide cutting-edge database solutions, ensuring seamless performance and robust scalability for businesses of all sizes. Our expertise spans across high-performance database management, optimization, and 24/7 support, helping you stay ahead in the digital era. Whether you’re managing large-scale data operations or need a reliable system to support your growing business, we’ve got you covered.",
      features: [
        { text: "Optimized Bandwidth" },
        { text: "Secure & Scalable Hosting" },
        { text: "Unmatched Performance" },
        { text: " 24/7 Expert Support" },
      ],
    },
    {
      title: "Back-end Development",
      icon: "/assets/backend.svg",
      bgColor: "bg-[#B16CEA1A]",
      borderColor: "border-[#B16CEA]",
      heading: "From Backend to Frontend, \nWe build it all",
      description:
        "Our backend development services are designed to power seamless, high-performance applications with robust architecture and scalable infrastructure. We specialize in building secure, efficient, and fully functional backend systems that support your business needs. From startups to enterprises, we create backend solutions that ensure reliability, security, and top-tier performance. Let’s build something great together!",
      features: [
        { text: "Scalable Architecture" },
        { text: " API Development & Integration" },
        { text: "Database Management" },
        { text: "Real-Time Data Processing" },
      ],
    },
    {
      title: "Front-end Development",
      icon: "/assets/frontend.svg",
      bgColor: "bg-[#FB56000D]",
      borderColor: "border-[#FA5A28]",
      heading: "Beautiful Interfaces, \nSeamless Experiences",
      description:
        "We craft visually stunning and highly intuitive front-end solutions that deliver seamless user experiences. Our expertise in modern UI/UX design and frontend development ensures your web and mobile applications are not only aesthetically appealing but also highly functional and responsive. From concept to execution, we transform ideas into immersive digital experiences that captivate and engage users. Let’s build something extraordinary!",
      features: [
        { text: " Pixel-Perfect UI Design" },
        { text: " Responsive & Adaptive Layouts" },
        { text: "Lightning-Fast Performance" },
        { text: "Interactive & Engaging UX" },
      ],
    },
    {
      title: "Design",
      icon: "/assets/design.svg",
      bgColor: "bg-[#455A641A]",
      borderColor: "border-[#455A64]",
      heading: "Creative Designs, \nTimeless Impact",
      description:
        "We blend innovation with aesthetics to create designs that leave a lasting impression. Our approach focuses on crafting visually compelling and strategically effective designs that elevate your brand identity and user engagement. Timeless design isn’t just about aesthetics—it’s about creating meaningful connections. Let’s turn your vision into reality! ",
      features: [
        { text: "Brand Identity & Logo Design" },
        { text: " UI/UX Design" },
        { text: "Web & App Design " },
        { text: "Marketing & Social Media Graphics" },
      ],
    },
  ];

  const handleInteraction = (title) => {
    setSelectedService(title);
  };

  const toggleAccordion = (title) => {
    setExpandedAccordion(expandedAccordion === title ? null : title);
  };

  const handleMouseEnter = (title) => {
    setSelectedService(title);
  };

  const handleMouseLeave = () => {
    setSelectedService(null); // Reset to dummy data
  };

  const renderServiceContent = (service) => (
    <div className="flex flex-col space-y-6">
      <p className="font-lato font-bold text-[30px] md:text-[40px] lg:text-[50px] leading-[45px] md:leading-[55px] lg:leading-[65px]">
        {service.heading.split("\n").map((line, i) => (
          <React.Fragment key={i}>
            {line.split(" ").map((word, j) => {
              const highlightWords = [
                "are",
                "build",
                "Seamless",
                "Timeless",
                "Solutions",
              ];
              return (
                <React.Fragment key={j}>
                  {highlightWords.includes(
                    word.replace(",", "").replace(".", "")
                  ) ? (
                    <span className="text-[#FF7731]">{word}</span>
                  ) : (
                    word
                  )}{" "}
                </React.Fragment>
              );
            })}
            {i < service.heading.split("\n").length - 1 && <br />}
          </React.Fragment>
        ))}
      </p>
      <p className="font-metrophobic font-normal text-sm md:text-base text-[#9B9B9B] leading-[28px] md:leading-[32px]">
        {service.description}
      </p>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-y-4 ">
        {service.features.map((feature, index) => (
          <div key={index} className="flex items-center gap-2">
            <svg
              className="w-4 h-4 text-[#FF7731]"
              viewBox="0 0 24 24"
              fill="currentColor"
            >
              <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
            </svg>
            <span className="font-lato text-[#333237] font-medium text-[16px] md:text-[18px]">
              {feature.text}
            </span>
          </div>
        ))}
      </div>
    </div>
  );

  return (
    <div
      id="service"
      className="relative overflow-hidden lg:h-[790px] xl:h-[700px] px-6 md:px-10 lg:px-20 mb-16 pb-5"
      onMouseLeave={handleMouseLeave}
    >
      <p className="font-lato text-[#FF7731] font-semibold text-[24px] md:text-[30px]">
        Our Services
      </p>
      <div className="flex justify-between items-center mt-4 gap-4 ">
        <p className="font-lato text-[#0E202B] font-extrabold text-[36px] md:text-[50px] lg:text-[60px]">
          How can we help?
        </p>
        <div className="flex justify-center items-center">
          {/* <div className="loader mr-10">
            <button className="flex justify-center items-center gap-1 md:gap-2 relative">
              <p className="w-[200px] sm:w-[250px] md:w-[300px] lg:w-[400px] h-[50px] sm:h-[60px] md:h-[80px] xl:h-[100px] -mr-[108px] md:-mr-[180px] lg:-mr-[260px]" />
              <p className="font-metrophobic whitespace-nowrap font-normal text-xs md:text-base lg:text-lg text-[#343434] z-10">
                View All Services
              </p>
              <img
                src="/assets/Btn_Arrow.svg"
                alt=""
                className="w-3 h-3 sm:w-4 sm:h-4 md:w-5 md:h-5 mt-1"
              />
            </button>
          </div> */}
        </div>
      </div>
      {isMobile ? (
        // Mobile Accordion Layout
        <div className="mt-10 space-y-4">
          {services.map((service) => (
            <div key={service.title}>
              {/* Accordion Container */}
              <div
                className={`${service.bgColor} border ${service.borderColor} rounded-lg overflow-hidden`}
              >
                {/* Accordion Header */}
                <div
                  onClick={() => toggleAccordion(service.title)}
                  className="flex items-center gap-4 w-full p-4 cursor-pointer"
                >
                  <span className="text-[#FF7731] font-semibold">
                    <img src={service.icon} alt="" />
                  </span>
                  <span className="font-lato text-[#333237] text-base font-bold flex-grow">
                    {service.title}
                  </span>
                  {expandedAccordion === service.title ? (
                    <ChevronUp className="w-5 h-5 text-[#333237]" />
                  ) : (
                    <ChevronDown className="w-5 h-5 text-[#333237]" />
                  )}
                </div>

                {/* Accordion Content */}
                <div
                  className={`overflow-hidden transition-[max-height] duration-500 ease-in-out ${
                    expandedAccordion === service.title
                      ? "max-h-[700px]"
                      : "max-h-0"
                  }`}
                >
                  <div className="p-4 pt-0">
                    {renderServiceContent(service)}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        // Desktop Layout
        <div className="relative flex flex-wrap lg:flex-nowrap justify-center  mt-10 gap-10 items-stretch">
          <div className="flex flex-col justify-start items-start lg:gap-8 xl:gap-4 w-full h-full lg:w-[35%]">
            {services.map((service, index) => (
              <div
                key={index}
                className={`flex items-center gap-4 w-full p-4 ${
                  service.bgColor
                } rounded-lg border ${
                  service.borderColor
                } transition duration-300 cursor-pointer ${
                  selectedService === service.title ? "scale-105" : ""
                }`}
                onMouseEnter={() => handleInteraction(service.title)}
                role="button"
                tabIndex={0}
              >
                <span className="text-[#FF7731] font-semibold">
                  <img src={service.icon} alt="" />
                </span>
                <span className="font-lato text-[#333237] text-base font-bold">
                  {service.title}
                </span>
              </div>
            ))}
          </div>
          <div className="flex flex-col justify-left items-left w-full lg:w-[60%] space-y-6 -mt-2">
            <AnimatePresence mode="wait">
              <motion.div
                key={selectedService || "default"}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.2 }}
              >
                {selectedService
                  ? renderServiceContent(
                      services.find((s) => s.title === selectedService)
                    )
                  : renderServiceContent({
                      heading:
                        "From Concept to Creation,\nWe Deliver Solutions",
                      title: "",
                      description:
                        "Explore our range of services designed to bring your vision to life. Hover over a service on the left to discover how we can help streamline your operations, enhance efficiency, and drive innovation. Whether it’s backend development, scalable solutions, or seamless integrations, we deliver tailored technology to meet your unique needs.",
                      features: [
                        { text: "Database Services" },
                        { text: "Back-end Development" },
                        { text: "Frontend-end Development" },
                        { text: "Design" },
                      ],
                    })}
              </motion.div>
            </AnimatePresence>
          </div>
        </div>
      )}
    </div>
  );
};

export default Services;
