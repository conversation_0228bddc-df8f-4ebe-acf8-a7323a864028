import React, { useState, useEffect } from "react";
import image_1 from "/assets/testimonials_Image_01.svg";
import image_2 from "/assets/testimonials_Image_02.svg";
import image_3 from "/assets/testimonials_Image_03.svg";

const Testimonials = () => {
  const testimonials = [
    {
      id: "01",
      name: "<PERSON><PERSON>",
      position: "top-20 left-[65%]",
      imageUrl: image_1,
      review:
        "Flexion Infotech solves the biggest problems with expertise. Flexion Infotech helps global brands with digital products on web, mobile, and connected platforms.",
      width: "w-48",
      height: "h-48",
      numPosition: "-top-6 -left-10",
      numSize: "text-[50px]",
      txtPosition: "bottom-10 left-32",
      margin: "md:mr-44"
      
    },
    {
      id: "02",
      name: "<PERSON>",
      position: "bottom-[10%] left-[65%]",
      imageUrl: image_2,
      review:
        "Flexion Infotech empowers businesses with cutting-edge solutions, simplifying technology to drive growth and efficiency for companies worldwide.",
      width: "w-44",
      height: "h-44",
      numPosition: "-top-6 -right-10",
      numSize: "text-[40px]",
      txtPosition: "bottom-10 right-32",
      margin: "md:mr-44"
    },
    {
      id: "03",
      name: "Oliver Reynolds",
      position: "bottom-20 right-[75%]",
      imageUrl: image_3,
      review:
        "Flexion Infotech delivers exceptional digital solutions, providing businesses with cutting-edge tools to thrive in a connected world.",
      width: "w-36",
      height: "h-36",
      numPosition: "-top-6 -right-10",
      numSize: "text-[30px]",
      txtPosition: "bottom-10 right-32",
      margin:'lg:ml-36 md:ml-72'
    },
  ];

  const [currentIndex, setCurrentIndex] = useState(0);

  // Auto-slide effect
  useEffect(() => {
    const interval = setInterval(() => {
      setCurrentIndex((prevIndex) => (prevIndex + 1) % testimonials.length);
    }, 3000);

    return () => clearInterval(interval);
  }, []);

  return (
    <div className="overflow-hidden font-montserrat bg-[#0E202B] md:min-h-screen relative px-6 sm:px-12 md:px-16 lg:px-24 py-16">
      {/* Main Text */}
      <div className="text-white max-w-4xl mb-16">
        <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-light mb-6">Our</h2>
        <div className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-bold mb-6">
          <span className="text-[#FF7731]">Client's</span>
          <span className="text-white"> are</span>
        </div>
        <h2 className="text-4xl sm:text-5xl md:text-6xl lg:text-8xl font-bold">Saying...</h2>
      </div>

      {/* Testimonial Circles for larger screens (md and above) */}
      <div className="hidden md:block">
        {testimonials.map((testimonial) => (
          <div
            key={testimonial.id}
            className={`absolute ${testimonial.position} group`}
          >
            {/* Circle with Number */}
            <div className="relative">
              <div
                className={`${testimonial.width} ${testimonial.height} rounded-full overflow-hidden relative cursor-pointer`}
              >
                <img
                  src={testimonial.imageUrl}
                  alt={testimonial.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Number */}
              <span
                className={`absolute ${testimonial.numPosition} text-white ${testimonial.numSize} font-medium`}
              >
                {testimonial.id}
              </span>

              {/* Review Popup */}
              <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
                <div
                  className={`invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all duration-300 bg-[#0E202B] text-black p-6 rounded-lg lg:w-[80vw] sm:w-[500px] max-w-[500px]  ${testimonial.margin} `}
                  style={{
                    zIndex: 50,
                    boxShadow:
                      "0px 0px 10px 2px rgba(255, 119, 49, 1), 0px 0px 10px 2px rgba(255, 255, 255, 0.2)",
                  }}
                
                >
                  <div className="flex flex-col sm:flex-row items-center gap-4">
                    {/* Image Section */}
                    <div className={`${testimonial.width} ${testimonial.height} rounded-full overflow-hidden relative flex-shrink-0`}>
                      <img
                        src={testimonial.imageUrl}
                        alt={testimonial.name}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Text Section */}
                    <div className="flex-1">
                      <p className="font-montserrat text-[#FFFFFF] text-sm sm:text-base md:text-lg mb-4 font-light">
                        {testimonial.review}
                      </p>
                      <span className="font-montserrat font-bold text-right block text-[#FFFFFF]">
                        {testimonial.name}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>

      {/* Mobile View: Auto-sliding Testimonials */}
      <div className="block md:hidden">
        <div className="relative overflow-hidden">
          <div 
            className="flex transition-transform duration-500 ease-in-out"
            style={{ 
              transform: `translateX(-${currentIndex * 100}%)`,
            }}
          >
            {testimonials.map((testimonial) => (
              <div
                key={testimonial.id}
                className="flex-shrink-0 w-full bg-[#1C2A36] text-white rounded-lg p-6 flex flex-col items-center gap-4"
              >
                <img
                  src={testimonial.imageUrl}
                  alt={testimonial.name}
                  className="w-32 h-32 object-cover rounded-full"
                />
                <p className="text-sm sm:text-base font-light">{testimonial.review}</p>
                <span className="font-bold text-lg">{testimonial.name}</span>
              </div>
            ))}
          </div>

          {/* Slide Indicators */}
          <div className="flex justify-center space-x-2 mt-4">
            {testimonials.map((_, index) => (
              <div
                key={index}
                className={`h-2 rounded-full transition-all duration-300 ${
                  currentIndex === index ? "w-4 bg-[#FF7731]" : "w-2 bg-gray-400"
                }`}
              />
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Testimonials;