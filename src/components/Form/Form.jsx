import React, { useState } from "react";
import formBg from "/assets/formBg.svg";
import bird_Logo from "/assets/logoBird.svg";

const ContactForm = () => {
  const [formData, setFormData] = useState({
    fullName: "",
    email: "",
    phone: "",
    category: "",
    budget: "",
    message: "",
  });

  const [errors, setErrors] = useState({});

  const handleInputChange = (e) => {
    e.preventDefault();
    const { name, value } = e.target;
    setFormData((prevData) => ({ ...prevData, [name]: value }));
  };

  const validate = () => {
    const newErrors = {};

    if (!formData.fullName.trim())
      newErrors.fullName = "Full name is required.";
    if (!formData.email.trim()) newErrors.email = "Email address is required.";
    else if (!/\S+@\S+\.\S+/.test(formData.email))
      newErrors.email = "Email address is invalid.";
    if (!formData.phone.trim()) newErrors.phone = "Phone number is required.";
    if (!formData.category.trim()) newErrors.category = "Category is required.";
    if (!formData.budget.trim()) newErrors.budget = "Budget is required.";
    if (!formData.message.trim()) newErrors.message = "Message is required.";

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    if (validate()) {
      console.log("Form submitted successfully:", formData);
      // Add your submission logic here (e.g., send data to server)

      setFormData({
        fullName: "",
        email: "",
        phone: "",
        category: "",
        budget: "",
        message: "",
      });
      setErrors({});
    }
  };

  return (
    <div
      id="contact-us"
      className="flex flex-col lg:flex-row w-full h-auto lg:h-screen bg-[#F9F8F2] mx-auto gap-8 p-6 lg:p-20"
    >
      {/* Left side - Company Info Card */}
      <div
        className="w-full lg:w-[35%] rounded-[25px] text-white p-8 relative overflow-hidden mx-auto"
        style={{
          backgroundImage: `url(${formBg})`,
          backgroundSize: "cover",
          backgroundPosition: "center",
        }}
      >
        <div className="relative flex flex-col justify-center  z-10">
          {/*logo */}
          <img src={bird_Logo} alt="logo" className="w-[70px] mx-auto" />
          {/*Slogan */}
          <div className="flex justify-center items-center lg:pt-10 pb-10 lg:pb-20">
            <p className="font-lato text-[#FFFFFF] text-[20px] lg:text-[30px] font-bold text-center">
              Think Tech, Choose Us !
            </p>
          </div>
          {/* Contact Information */}
          <div className="space-y-10 flex flex-col justify-center mx-auto lg:mt-14">
            {/* Email Section */}
            <div className="flex items-start gap-4">
              <div className="p-2 bg-orange-500 rounded-lg">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M3 8l7.89 5.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                  />
                </svg>
              </div>
              <div>
                <p className="font-metrophobic font-normal text-[14px] text-[#FF7731]">
                  Send
                </p>
                <p className="font-metrophobic font-normal text-[15px]">
                  <EMAIL>
                </p>
              </div>
            </div>

            {/* Address Section */}
            <div className="flex items-start gap-4">
              <div className="p-2 bg-orange-500 rounded-lg">
                <svg
                  className="w-6 h-6 text-white"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z"
                  />
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M15 11a3 3 0 11-6 0 3 3 0 016 0z"
                  />
                </svg>
              </div>
              <div>
                <p className="font-metrophobic font-normal text-[14px] text-[#FF7731]">
                  Address
                </p>
                <p className="font-metrophobic font-normal text-[14px] leading-[30px]">
                  303, Green Plaza, Digital Valley, Mota Varachha
                </p>
                <p className="font-metrophobic font-normal text-[14px] leading-[30px]">
                  Surat, Gujarat 394105
                </p>
              </div>
            </div>
          </div>
        </div>

        {/* Background decoration */}
        <div className="absolute bottom-0 right-0 w-32 h-32 bg-gray-700 rounded-full opacity-10"></div>
      </div>

      {/* Right side - Form */}
      <div className="w-full lg:w-[65%] px-4 lg:px-10 ">
        <form className="space-y-6" onSubmit={handleSubmit}>
          <div>
            <label className="font-metrophobic text-[#263238] block text-sm font-normal mb-2">
              Full name
            </label>
            <input
              type="text"
              name="fullName"
              value={formData.fullName}
              onChange={handleInputChange}
              className="w-full p-3 border rounded-md bg-[#F9F8F2] focus:outline-none focus:ring-2 focus:ring-[#FF7731] "
            />
            {errors.fullName && (
              <p className="text-red-500 text-sm">{errors.fullName}</p>
            )}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label className="font-metrophobic text-[#263238] block text-sm font-normal mb-2">
                Email address
              </label>
              <input
                type="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                className="w-full p-3 border rounded-md bg-[#F9F8F2] focus:outline-none focus:ring-2 focus:ring-[#FF7731]"
              />
              {errors.email && (
                <p className="text-red-500 text-sm">{errors.email}</p>
              )}
            </div>
            <div>
              <label className="font-metrophobic text-[#263238] block text-sm font-normal mb-2">
                Phone number
              </label>
              <input
                type="tel"
                name="phone"
                value={formData.phone}
                onChange={handleInputChange}
                className="w-full p-3 border rounded-md bg-[#F9F8F2] focus:outline-none focus:ring-2 focus:ring-[#FF7731]"
              />
              {errors.phone && (
                <p className="text-red-500 text-sm">{errors.phone}</p>
              )}
            </div>
          </div>

          {/* <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            <div>
              <label className="font-metrophobic text-[#263238] block text-sm font-normal mb-2">
                Category
              </label>
              <input
                type="text"
                name="category"
                value={formData.category}
                onChange={handleInputChange}
                className="w-full p-3 border rounded-md bg-[#F9F8F2] focus:outline-none focus:ring-2 focus:ring-[#FF7731]"
              />
              {errors.category && (
                <p className="text-red-500 text-sm">{errors.category}</p>
              )}
            </div>
            <div>
              <label className="font-metrophobic text-[#263238] block text-sm font-normal mb-2">
                Budget
              </label>
              <input
                type="text"
                name="budget"
                value={formData.budget}
                onChange={handleInputChange}
                className="w-full p-3 border rounded-md bg-[#F9F8F2] focus:outline-none focus:ring-2 focus:ring-[#FF7731]"
              />
              {errors.budget && (
                <p className="text-red-500 text-sm">{errors.budget}</p>
              )}
            </div>
          </div> */}

          <div>
            <label className="font-metrophobic text-[#263238] block text-sm font-normal mb-2">
              Message
            </label>
            <textarea
              name="message"
              value={formData.message}
              onChange={handleInputChange}
              className="w-full p-3 border rounded-md h-96 bg-[#F9F8F2] focus:outline-none focus:ring-2 focus:ring-[#FF7731]"
            ></textarea>
            {errors.message && (
              <p className="text-red-500 text-sm">{errors.message}</p>
            )}
          </div>

          <div className="flex justify-center items-center">
            <button
              type="submit"
              className="font-metrophobic font-normal bg-[#0E202B] text-white py-3 px-8 lg:px-16 rounded-full hover:bg-[#1B3243] transition-colors"
            >
              Submit
            </button>
          </div>
        </form>
      </div>
    </div>
  );
};

export default ContactForm;
