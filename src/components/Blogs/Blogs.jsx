import React from "react";
import blogArrow from "/assets/blogArrow.svg";
import blogImage from "/assets/blogImage_02.svg";
import button_Arrow from "/assets/Btn_Arrow.svg";
import flutter from "/assets/flutter.svg";
import python from "/assets/python.svg";
import react from "/assets/react.svg";
import link from "/assets/link.svg";
import arrowDown from "/assets/arrowDown.svg";

function Blogs() {
  return (
    <div id="blog" className="overflow-hidden px-6 md:px-10 lg:px-20 mb-20">
      {/* Top Text - Heading*/}
      <p className="font-lato text-[#0E202B] font-semibold text-[24px] md:text-[30px] mt-10">
        Insights
      </p>

      {/*Top Text - SubHeading*/}
      <div className="flex justify-between items-center mt-4 gap-4">
        <p className="font-lato text-[#0E202B] font-extrabold text-[36px] md:text-[50px] lg:text-[60px]">
          And tips <span className="text-[#FF7731]">just for</span> you
        </p>
        {/* Button */}
        {/* <div className="flex justify-center items-center">
          <button className="flex justify-center items-center gap-1 md:gap-2 ">
            <p className="loader w-[250px] md:w-[300px] xl:w-[400px] h-[50px] sm:h-[60px] md:h-[80px] -mr-[75px]  md:-mr-[177px] lg:-mr-[178px] xl:-mr-[228px]" />
            <p className="font-metrophobic whitespace-nowrap font-normal text-xs  md:text-base lg:text-lg text-[#343434]">
              Read more blog
            </p>
            <img
              src={button_Arrow}
              alt="Arrow"
              className="w-3 h-3 sm:w-4 sm:h-4 md:w-6 md:h-6 mt-1"
            />
          </button>
        </div> */}
      </div>

      {/*Container */}
      <div className="flex flex-wrap lg:flex-nowrap justify-center items-start mt-10 gap-10 h-full p-5">
        {/* Left Div */}
        <div className="flex flex-col mx-auto md:justify-start md:items-start gap-6 w-full lg:w-[35%] h-full">
          {/* Card */}
          <div className="group  overflow-hidden  bg-[#CECBD014] rounded-2xl shadow-lg border border-gray-200 p-4 space-y-4 relative transition duration-300">
            {/* Title */}
            <div className="flex items-center justify-between">
              <h3 className="text-[30px] font-bold font-lato">
                <span className="text-[#FF7731]">Front end </span>
                <span className="text-[#0E202B]">Development</span>
              </h3>
              {/* Icon Button */}
              <div className="w-14 h-14 bg-[#0E202B] rounded-full flex justify-center items-center cursor-pointer  ">
                <img
                  src={blogArrow}
                  alt=""
                  className="hover:rotate-45 transition-transform duration-300"
                />
              </div>
            </div>

            <div className="w-full h-[2px] bg-gradient-to-l from-transparent via-[#F0F0F0] to-transparent"></div>

            {/* Description */}
            <p className="text-sm font-metrophobic text-[#333237] leading-[40px]">
              Flexion Infotech played a crucial role in revitalizing a
              pioneering leader in the horticulture industry, enhancing its
              digital presence and optimizing operations for sustainable growth
              and innovation.
            </p>

            {/* Image */}
            <img
              src={blogImage}
              alt="Development"
              className="w-full h-44 object-cover rounded-lg"
            />
          </div>
        </div>

        {/* Right Div */}
        <div className="w-full lg:w-[65%] bg-white h-full">
          {/* Technology Pills */}
          <div className="flex flex-wrap gap-4 mb-8 justify-center xl:justify-start">
            {/* Flutter Pill */}
            <div className="flex items-center text-left gap-2 px-3 py-2 rounded-full bg-[#68B7F721] border border-[#68B7F7] sm:flex-none sm:w-auto w-full max-w-[300px] group hover:scale-105 hover:bg-[#68B7F7] hover:border-[#68B7F7] transition-all duration-300">
              <div className="flex-shrink-0">
                <img
                  src={flutter}
                  alt="Flutter"
                  className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12"
                />
              </div>
              <span className="font-metrophobic flex-grow text-center pl-4 pr-6 sm:pr-8 text-[#000000] font-normal text-[16px] sm:text-[18px] lg:text-[22px]">
                Flutter
              </span>
            </div>

            {/* Python Pill */}
            <div className="flex items-center gap-2 px-3 py-2 rounded-full bg-[#FFD64726] border border-[#FFD140] sm:flex-none sm:w-auto w-full max-w-[300px] group hover:scale-105 hover:bg-[#FFD647] hover:border-[#FFD140] transition-all duration-300">
              <div className="flex-shrink-0">
                <img
                  src={python}
                  alt="Python"
                  className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12"
                />
              </div>
              <span className="font-metrophobic flex-grow text-center pl-4 pr-6 sm:pr-8 text-[#000000] font-normal text-[16px] sm:text-[18px] lg:text-[22px]">
                Python
              </span>
            </div>

            {/* React JS Pill */}
            <div className="flex items-center gap-2 px-3 py-2 rounded-full bg-[#BBBBBB26] border border-[#BBBBBB] sm:flex-none sm:w-auto w-full max-w-[300px] group hover:scale-105 hover:bg-[#BBBBBB] hover:border-[#BBBBBB] transition-all duration-300">
              <div className="flex-shrink-0">
                <img
                  src={react}
                  alt="React JS"
                  className="w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12"
                />
              </div>
              <span className="font-metrophobic flex-grow text-center pl-4 pr-6 sm:pr-8 text-[#000000] font-normal text-[16px] sm:text-[18px] lg:text-[22px]">
                React Js
              </span>
            </div>
          </div>

          {/* Title */}
          <h2 className="font-lato text-[32px] font-semibold text-[#000000] leading-[50px] mb-6">
            Meet your Glide Certified community challenge winners
          </h2>

          {/* Description */}
          <p className="font-metrophobic text-[#000000] text-base leading-[40px] mb-8">
            Unlock your full potential with Glide! Build apps effortlessly,
            streamline your workflow, and bring your ideas to life. Watch your
            projects progress smoothly as you turn complex tasks into simple,
            actionable steps. Empower yourself today!
          </p>

          {/* Bottom Link and Button */}
          <div className="bg-[#F4F4F4] rounded-[42px] flex flex-col sm:flex-row justify-between items-center p-4 lg:p-4 gap-4 sm:gap-6">
            {/* Link Section */}
            <div className="flex justify-center items-center gap-2 sm:gap-3 w-full sm:w-auto">
              <img
                src={link}
                alt="icon"
                className="w-6 h-6 sm:w-8 sm:h-8 lg:w-10 lg:h-10 flex-shrink-0"
              />
              <span className="font-lato text-[#FF7731] text-[12px] sm:text-[14px] lg:text-[16px] font-bold truncate">
                <a
                  href="https://medium.com/@flexion_infotech"
                  className="hover:underline"
                >
                  https://medium.com/@flexion_infotech
                </a>
              </span>
            </div>

            {/* Button */}
            <button className="flex justify-center items-center gap-2 sm:gap-3 bg-[#0E202B] text-white px-4 py-2 rounded-full font-medium hover:bg-opacity-90 text-sm sm:text-base w-full sm:w-auto group">
              Join Us
              <img
                src={arrowDown}
                alt="arrow"
                className="w-4 h-4 sm:w-5 sm:h-5 transition-transform duration-300 group-hover:-rotate-45"
              />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}

export default Blogs;
