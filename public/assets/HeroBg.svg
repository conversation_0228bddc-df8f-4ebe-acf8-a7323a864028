<svg width="1440" height="912" viewBox="0 0 1440 912" fill="none" xmlns="http://www.w3.org/2000/svg">
<g clip-path="url(#clip0_3159_396)">
<rect width="1440" height="912" fill="#F9F8F2"/>
<path  d="M552.501 711.917H902.453M0.530263 117.511L0.53023 834M47.1906 117.511L47.1905 834M93.8509 117.511L93.8508 834M140.511 117.511L140.511 834M187.172 117.511L187.171 834M233.832 117.511L233.832 834M280.492 117.511L280.492 834M327.152 117.511L327.152 834M373.813 117.511L373.813 834M0 805.481H1446.47M420.473 117.511L420.473 834M0 761.451H1446.47M467.133 117.511L467.133 834M0 717.42H1446.47M513.794 117.511L513.794 834M0 673.39H1446.47M560.454 117.511L560.454 834M0 629.36H1446.47M607.114 117.511L607.114 834M0 585.33H1446.47M653.775 117.511L653.775 834M0 541.3H1446.47M700.435 117.511L700.435 834M0 497.27H1446.47M747.095 117.511L747.095 834M0 453.24H1446.47M793.756 117.511L793.756 834M0 409.21H1446.47M840.416 117.511L840.416 834M0 365.18H1446.47M887.076 117.511L887.076 834M0 321.15H1446.47M933.737 117.511L933.737 834M0 277.12H1446.47M980.397 117.511L980.397 834M0 233.09H1446.47M1027.06 117.511L1027.06 834M0 189.06H1446.47M1073.72 117.511L1073.72 834M0 145.03H1446.47M1120.38 117.511V834M0 101L1446.47 101M1167.04 117.511V834M1213.7 117.511V834M1260.36 117.511V834M1307.02 117.511V834M1353.68 117.511V834M1400.34 117.511V834M1447 117.511V834" stroke="url(#paint0_radial_3159_396)"/>
<path d="M552.501 931.917H902.453M0.530263 337.511L0.53023 1054M47.1906 337.511L47.1905 1054M93.8509 337.511L93.8508 1054M140.511 337.511L140.511 1054M187.172 337.511L187.171 1054M233.832 337.511L233.832 1054M280.492 337.511L280.492 1054M327.152 337.511L327.152 1054M373.813 337.511L373.813 1054M0 1025.48H1446.47M420.473 337.511L420.473 1054M0 981.451H1446.47M467.133 337.511L467.133 1054M0 937.42H1446.47M513.794 337.511L513.794 1054M0 893.39H1446.47M560.454 337.511L560.454 1054M0 849.36H1446.47M607.114 337.511L607.114 1054M0 805.33H1446.47M653.775 337.511L653.775 1054M0 761.3H1446.47M700.435 337.511L700.435 1054M0 717.27H1446.47M747.095 337.511L747.095 1054M0 673.24H1446.47M793.756 337.511L793.756 1054M0 629.21H1446.47M840.416 337.511L840.416 1054M0 585.18H1446.47M887.076 337.511L887.076 1054M0 541.15H1446.47M933.737 337.511L933.737 1054M0 497.12H1446.47M980.397 337.511L980.397 1054M0 453.09H1446.47M1027.06 337.511L1027.06 1054M0 409.06H1446.47M1073.72 337.511L1073.72 1054M0 365.03H1446.47M1120.38 337.511V1054M0 321L1446.47 321M1167.04 337.511V1054M1213.7 337.511V1054M1260.36 337.511V1054M1307.02 337.511V1054M1353.68 337.511V1054M1400.34 337.511V1054M1447 337.511V1054" stroke="url(#paint1_radial_3159_396)"/>
<path opacity="0.2" class="wave" d="M206 379.41C230.812 418.401 294.744 475.888 351.974 393.911C389.031 325.274 480.353 217.582 549.183 335.908L582.051 407.444C588.818 426.779 609.699 451.141 639.087 393.911C645.593 382.199 659.246 360.525 670.369 350.989C674.75 347.233 677.05 343.785 680.955 348.036V348.036C691.25 359.243 672.011 381.376 657.173 384.76C643.459 387.889 632.808 383.142 642.954 355.241C646.176 348.796 657.648 334.553 677.756 329.14C686.521 327.222 703.016 328.843 708.716 346.948C710.987 354.158 711.206 362.116 715.34 368.445C721.663 378.125 733.441 385.992 751.711 376.728C757.435 373.825 762.079 369.258 766.774 364.882C778.403 354.046 793.431 343.505 792.794 358.141L795.695 432.578C796.984 445.468 806.328 462.739 833.396 428.711C838.056 422.013 844.277 413.28 850.886 404.324C872.115 375.559 900.08 380.767 928.681 402.218C933.697 405.98 938.749 409.154 943.602 411.31C951.031 414.439 965.034 418.113 979.907 417.236C999.538 416.079 1018.28 404.801 1034.34 393.459C1041.17 388.634 1049.53 382.396 1059.62 374.412C1066.7 368.803 1074.43 364.025 1082.7 360.376L1182.74 316.22C1200.18 308.521 1221.86 294.67 1203.81 288.529V288.529C1188 283.15 1162.99 306.232 1163.53 322.924C1164.73 360.034 1212.34 344.981 1233.62 341.707" stroke="url(#paint2_linear_3159_396)" stroke-width="1.88757"/>
</g>
<defs>
<radialGradient id="paint0_radial_3159_396" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(723.5 424) rotate(90) scale(410 809.372)">
<stop stop-opacity="0.12"/>
<stop offset="0.6" stop-color="#B5B5B5" stop-opacity="0"/>
</radialGradient>
<radialGradient id="paint1_radial_3159_396" cx="0" cy="0" r="1" gradientUnits="userSpaceOnUse" gradientTransform="translate(723.5 644) rotate(90) scale(410 809.372)">
<stop stop-opacity="0.12"/>
<stop offset="0.6" stop-color="#B5B5B5" stop-opacity="0"/>
</radialGradient>
<linearGradient id="paint2_linear_3159_396" x1="206" y1="368.184" x2="1237.82" y2="514.74" gradientUnits="userSpaceOnUse">
<stop stop-color="#E85A28"/>
<stop offset="0.465" stop-color="#D7173A"/>
<stop offset="0.98" stop-color="#BA0774"/>
</linearGradient>
<clipPath id="clip0_3159_396">
<rect width="1440" height="912" fill="white"/>
</clipPath>
</defs>
<style>
    .wave {
         stroke-dasharray: 1000; /* Adjust this value based on your path length */
  stroke-dashoffset: 1000; /* Start position */
      animation: waveEffect 5s infinite linear;
    }

    @keyframes waveEffect {
      0% {
        stroke-dashoffset: 1000;
      }
      50% {
        stroke-dashoffset: 0;
      }
      100% {
        stroke-dashoffset: -1000;
      }
    }
  </style>
</svg>
