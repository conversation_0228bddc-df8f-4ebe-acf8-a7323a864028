<svg
  width="1440"
  height="912"
  viewBox="0 0 1440 912"
  fill="none"
  xmlns="http://www.w3.org/2000/svg"
>
  <g clip-path="url(#clip0_1_4)">
    <rect width="1440" height="912" fill="#F9F8F2" />
    <!-- Moving Wave Path -->
    <path
      class="wave"
      opacity="0.3"
      d="M227.764 378.41C252.576 417.401 316.508 474.888 373.737 392.911C410.795 324.274 502.117 216.582 570.947 334.908L603.815 406.444C610.582 425.779 631.463 450.141 660.851 392.911C668.907 378.41 687.919 348.634 699.519 345.541C703.386 345.541 708.993 351.341 700.486 374.542C682.119 385.498 649.25 396.776 664.718 354.241C667.94 347.796 679.412 333.553 699.519 328.14C709.831 325.884 730.841 328.525 732.388 357.141C736.254 370.675 751.528 392.33 781.69 370.675C792.968 358.752 815.332 339.354 814.558 357.141L817.458 431.578C818.747 444.468 828.092 461.739 855.16 427.711C870.627 405.477 903.302 360.815 910.263 360.041C919.93 372.931 944.484 401.03 965.365 410.31C977.61 415.466 1007.71 422.104 1030.14 407.41C1033.36 407.41 1050.24 398.903 1092 364.875L1204.5 315.22C1221.95 307.521 1243.63 293.67 1225.58 287.529V287.529C1209.76 282.15 1184.75 305.232 1185.29 321.924C1186.49 359.034 1234.1 343.981 1255.38 340.707"
      stroke="url(#paint0_linear_1_4)"
      stroke-width="1.48757"
      stroke-dasharray="1000"
      stroke-dashoffset="0"
    />
    <path
      d="M552.501 711.917H902.453M0.530263 117.511L0.53023 834M47.1906 117.511L47.1905 834M93.8509 117.511L93.8508 834M140.511 117.511L140.511 834M187.172 117.511L187.171 834M233.832 117.511L233.832 834M280.492 117.511L280.492 834M327.152 117.511L327.152 834M373.813 117.511L373.813 834M0 805.481H1446.47M420.473 117.511L420.473 834M0 761.451H1446.47M467.133 117.511L467.133 834M0 717.42H1446.47M513.794 117.511L513.794 834M0 673.39H1446.47M560.454 117.511L560.454 834M0 629.36H1446.47M607.114 117.511L607.114 834M0 585.33H1446.47M653.775 117.511L653.775 834M0 541.3H1446.47M700.435 117.511L700.435 834M0 497.27H1446.47M747.095 117.511L747.095 834M0 453.24H1446.47M793.756 117.511L793.756 834M0 409.21H1446.47M840.416 117.511L840.416 834M0 365.18H1446.47M887.076 117.511L887.076 834M0 321.15H1446.47M933.737 117.511L933.737 834M0 277.12H1446.47M980.397 117.511L980.397 834M0 233.09H1446.47M1027.06 117.511L1027.06 834M0 189.06H1446.47M1073.72 117.511L1073.72 834M0 145.03H1446.47M1120.38 117.511V834M0 101L1446.47 101M1167.04 117.511V834M1213.7 117.511V834M1260.36 117.511V834M1307.02 117.511V834M1353.68 117.511V834M1400.34 117.511V834M1447 117.511V834"
      stroke="url(#paint1_radial_1_4)"
    />
  </g>
  <defs>
    <linearGradient
      id="paint0_linear_1_4"
      x1="227.764"
      y1="367.184"
      x2="1259.58"
      y2="513.74"
      gradientUnits="userSpaceOnUse"
    >
      <stop stop-color="#E85A28" />
      <stop offset="0.465" stop-color="#D7173A" />
      <stop offset="0.98" stop-color="#BA0774" />
    </linearGradient>
    <radialGradient
      id="paint1_radial_1_4"
      cx="0"
      cy="0"
      r="1"
      gradientUnits="userSpaceOnUse"
      gradientTransform="translate(723.5 424) rotate(90) scale(410 809.372)"
    >
      <stop stop-opacity="0.12" />
      <stop offset="0.6" stop-color="#B5B5B5" stop-opacity="0" />
    </radialGradient>
    <clipPath id="clip0_1_4">
      <rect width="1440" height="912" fill="white" />
    </clipPath>
  </defs>
  <style>
    .wave {
      animation: waveEffect 5s infinite linear;
    }

    @keyframes waveEffect {
      0% {
        stroke-dashoffset: 1000;
      }
      50% {
        stroke-dashoffset: 0;
      }
      100% {
        stroke-dashoffset: -1000;
      }
    }
  </style>
</svg>
    