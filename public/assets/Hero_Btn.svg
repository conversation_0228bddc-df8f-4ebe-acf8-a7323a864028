<svg width="209" height="126" viewBox="0 0 209 126" fill="none" xmlns="http://www.w3.org/2000/svg">
  <!-- Circle with animation -->
  <circle class="circle" opacity="0.1" cx="62.8136" cy="63.001" r="49.623" fill="white" fill-opacity="0.26" stroke="url(#paint0_linear_1_3)" stroke-width="1.25628"/>
  
  <!-- Path with animation -->
  <path class="path" d="M119.522 88.5202C113.618 101.64 103.348 112.307 90.4611 118.703C77.5742 125.1 62.8681 126.83 48.8486 123.599C34.8291 120.368 22.3636 112.376 13.5761 100.984C4.7885 89.5929 0.222633 75.5069 0.656411 61.1264C1.09019 46.746 6.49678 32.9608 15.955 22.1197C25.4131 11.2787 38.3377 4.05251 52.5265 1.67247C66.7152 -0.707579 81.2903 1.90577 93.7682 9.06723C106.246 16.2287 115.855 27.4952 120.957 40.947" stroke="url(#paint1_linear_1_3)" stroke-width="1.25628"/>
  
  <defs>
    <linearGradient id="paint0_linear_1_3" x1="12.5625" y1="64.4367" x2="115.458" y2="66.7908" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E85A28"/>
      <stop offset="0.465" stop-color="#D7173A"/>
      <stop offset="0.98" stop-color="#BA0774"/>
    </linearGradient>
    <linearGradient id="paint1_linear_1_3" x1="1.37369e-07" y1="64.7961" x2="128.619" y2="67.7386" gradientUnits="userSpaceOnUse">
      <stop stop-color="#E85A28"/>
      <stop offset="0.465" stop-color="#D7173A"/>
      <stop offset="0.98" stop-color="#BA0774"/>
    </linearGradient>
  </defs>

</svg>
