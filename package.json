{"name": "flexion-web", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "framer-motion": "^11.18.2", "lucide-react": "^0.474.0", "react": "^18.3.1", "react-confetti": "^6.4.0", "react-dom": "^18.3.1", "react-router-dom": "^7.7.0", "react-slick": "^0.30.3", "react-use": "^17.6.0", "slick-carousel": "^1.8.1", "swiper": "^11.2.1"}, "devDependencies": {"@eslint/js": "^9.17.0", "@types/react": "^18.3.18", "@types/react-dom": "^18.3.5", "@vitejs/plugin-react": "^4.3.4", "autoprefixer": "^10.4.20", "eslint": "^9.17.0", "eslint-plugin-react": "^7.37.2", "eslint-plugin-react-hooks": "^5.0.0", "eslint-plugin-react-refresh": "^0.4.16", "globals": "^15.14.0", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "vite": "^6.0.5"}}